'use client';

import { But<PERSON> } from '@telegram-apps/telegram-ui';
import { Handshake, Plus } from 'lucide-react';
import { useEffect, useState } from 'react';

import { AuthWrapper } from '@/components/auth/auth-wrapper';
import { MarketplaceFilters } from '@/components/shared/marketplace-filters';
import { MarketplaceOrderList } from '@/components/shared/marketplace-order-list';
import { Tabs, TabsContent } from '@/components/ui/tabs';
import {
  CachePatterns,
  type OrderEntity,
  UserType,
} from '@/constants/core.constants';
import { useAppCache } from '@/contexts/AppCacheContext';
import { useOrderListFilters } from '@/contexts/OrderListFiltersContext';
import { useInfiniteScroll } from '@/hooks/use-infinite-scroll';
import { useScrollPreservation } from '@/hooks/use-scroll-preservation';
import { useRootContext } from '@/root-context';

import { CreateOrderDrawer } from './marketplace/create-order-drawer';
import type { TabType } from './marketplace/hooks/use-marketplace-orders';
import { useMarketplaceOrders } from './marketplace/hooks/use-marketplace-orders';
import { MarketplaceTabs } from './marketplace/marketplace-tabs';
import { OrderDetailsDrawer } from './marketplace/order-details-drawer';
import { ResellOrderDrawer } from './marketplace/resell/resell-order-drawer';

export function MarketplaceContent() {
  const { refetchUser, currentUser } = useRootContext();
  const cache = useAppCache();
  const [activeTab, setActiveTab] = useState<TabType>('buyers');
  const [showCreateOrderDrawer, setShowCreateOrderDrawer] = useState(false);
  const [showOrderDetailsDrawer, setShowOrderDetailsDrawer] = useState(false);
  const [showResellDrawer, setShowResellDrawer] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<OrderEntity | null>(null);

  useScrollPreservation({ isOpen: showCreateOrderDrawer });
  useScrollPreservation({ isOpen: showOrderDetailsDrawer });
  useScrollPreservation({ isOpen: showResellDrawer });

  const { debouncedFilters } = useOrderListFilters();
  const ordersFilters = {
    ...debouncedFilters,
    currentUserId: currentUser?.id,
  };

  const { sellersState, buyersState, loadOrders, loadMoreOrders } =
    useMarketplaceOrders({
      filters: ordersFilters,
      activeTab,
    });

  const { containerRef } = useInfiniteScroll({
    hasNextPage:
      activeTab === 'buyers' ? buyersState.hasMore : sellersState.hasMore,
    isFetchingNextPage:
      activeTab === 'buyers' ? buyersState.loading : sellersState.loading,
    fetchNextPage: () => loadMoreOrders(activeTab),
  });

  useEffect(() => {
    loadOrders(activeTab);
  }, [activeTab, ordersFilters, loadOrders]);

  const handleOrderClick = (order: OrderEntity) => {
    setSelectedOrder(order);
    setShowOrderDetailsDrawer(true);
  };

  const handleOrderAction = () => {
    cache.invalidatePattern(CachePatterns.ORDERS_FOR_BUYERS);
    cache.invalidatePattern(CachePatterns.ORDERS_FOR_SELLERS);
    cache.invalidatePattern(CachePatterns.SECONDARY_MARKET_ORDERS);
    refetchUser();
    loadOrders(activeTab);
  };

  const handleCreateOrderSuccess = () => {
    setShowCreateOrderDrawer(false);
    handleOrderAction();
  };

  const handleResellSuccess = () => {
    setShowResellDrawer(false);
    handleOrderAction();
  };

  const handleCreateOrderClick = () => {
    setShowCreateOrderDrawer(true);
  };

  const handleResellClick = (order: OrderEntity) => {
    setSelectedOrder(order);
    setShowResellDrawer(true);
  };

  return (
    <div className="container mx-auto px-4 py-6 space-y-6" ref={containerRef}>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Handshake className="h-6 w-6 text-[#6ab2f2]" />
          <h1 className="text-2xl font-bold text-[#f5f5f5]">Marketplace</h1>
        </div>

        <AuthWrapper>
          <Button
            size="s"
            className="bg-[#6ab2f2] text-white hover:bg-[#5a9fd9] [&>h6]:flex [&>h6]:items-center [&>h6]:gap-2"
            onClick={handleCreateOrderClick}
          >
            <Plus className="h-4 w-4" />
            Create Order
          </Button>
        </AuthWrapper>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab as any}>
        <MarketplaceTabs activeTab={activeTab} onTabChange={setActiveTab} />

        <MarketplaceFilters />

        <TabsContent value="sellers" className="space-y-4">
          <MarketplaceOrderList
            orders={sellersState.orders}
            loading={sellersState.loading}
            error={sellersState.error}
            onOrderClick={handleOrderClick}
            activeTab={activeTab}
            userType={UserType.BUYER}
          />
        </TabsContent>

        <TabsContent value="buyers" className="space-y-4">
          <MarketplaceOrderList
            orders={buyersState.orders}
            loading={buyersState.loading}
            error={buyersState.error}
            onOrderClick={handleOrderClick}
            activeTab={activeTab}
            userType={UserType.SELLER}
          />
        </TabsContent>
      </Tabs>

      <CreateOrderDrawer
        open={showCreateOrderDrawer}
        onOpenChange={setShowCreateOrderDrawer}
        onOrderCreated={handleCreateOrderSuccess}
      />

      <OrderDetailsDrawer
        open={showOrderDetailsDrawer}
        onOpenChange={setShowOrderDetailsDrawer}
        order={selectedOrder}
        userType={activeTab === 'buyers' ? UserType.SELLER : UserType.BUYER}
        onOrderAction={handleOrderAction}
      />

      <ResellOrderDrawer
        open={showResellDrawer}
        onOpenChange={setShowResellDrawer}
        order={selectedOrder}
        onResellSuccess={handleResellSuccess}
        onResellClick={handleResellClick}
      />
    </div>
  );
}
