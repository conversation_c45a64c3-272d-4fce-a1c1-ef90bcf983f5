'use client';

import { OrderListFiltersProvider } from '@/contexts/OrderListFiltersContext';
import { useRootContext } from '@/root-context';

import { MarketplaceContent } from './marketplace-content';

export default function MarketplacePage() {
  const { collections } = useRootContext();

  return (
    <OrderListFiltersProvider collections={collections}>
      <MarketplaceContent />
    </OrderListFiltersProvider>
  );
}
    hasMore: sellersState.hasMore,
    loading: sellersState.loading || sellersState.loadingMore,
    onLoadMore: () => {
      if (activeTab === 'sellers') {
        loadMoreOrders();
      }
    },
  });

  const buyersLoadMoreRef = useInfiniteScroll({
    hasMore: buyersState.hasMore,
    loading: buyersState.loading || buyersState.loadingMore,
    onLoadMore: () => {
      if (activeTab === 'buyers') {
        loadMoreOrders();
      }
    },
  });

  useEffect(() => {
    loadOrders(true);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeTab, currentUser]);

  // Reload orders when filters change
  useEffect(() => {
    loadOrders(true);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    ordersFilters.minPrice,
    ordersFilters.maxPrice,
    ordersFilters.collectionId,
    ordersFilters.sortBy,
  ]);

  const handleCreateOrder = () => {
    setShowCreateOrderDrawer(true);
  };

  const handleOrderCreated = () => {
    cache.invalidatePattern(CachePatterns.ORDERS_FOR_BUYERS);
    cache.invalidatePattern(CachePatterns.ORDERS_FOR_SELLERS);
    cache.invalidatePattern(CachePatterns.SECONDARY_MARKET_ORDERS);
    loadOrders(true);
    refetchUser();
  };

  const handleOrderClick = (order: OrderEntity) => {
    setSelectedOrder(order);
    setShowOrderDetailsDrawer(true);
  };

  const handleOrderAction = handleOrderCreated;

  const handleResellClick = () => {
    setShowResellDrawer(true);
  };

  const handleOrderResold = () => {
    handleOrderCreated();
  };

  return (
    <div className="relative space-y-2 bg-[#17212b] min-h-screen">
      {activeTab === 'buyers' && (
        <AuthWrapper>
          <Button
            onClick={handleResellClick}
            className="fixed! rounded-[50%]! bottom-[120px] right-2 w-[42px]! h-[42px]! p-0! flex justify-center bg-green-500 hover:bg-green-600 z-50"
          >
            <Handshake size={20} />
          </Button>
        </AuthWrapper>
      )}

      <AuthWrapper>
        <Button
          onClick={handleCreateOrder}
          className="fixed! rounded-[50%]! bottom-16 right-2 w-[42px]! h-[42px]! p-0! flex justify-center z-50"
        >
          <Plus />
        </Button>
      </AuthWrapper>
      <Tabs value={activeTab}>
        <MarketplaceTabs activeTab={activeTab} onTabChange={setActiveTab} />

        <MarketplaceFilters
          minPrice={filters.minPrice}
          maxPrice={filters.maxPrice}
          selectedCollection={filters.selectedCollection}
          sortBy={filters.sortBy}
          collections={collections}
          onMinPriceChange={filters.setMinPrice}
          onMaxPriceChange={filters.setMaxPrice}
          onCollectionChange={filters.setSelectedCollection}
          onSortChange={filters.setSortBy}
        />

        <TabsContent value="sellers" className="space-y-4">
          <MarketplaceOrderList
            variant="order"
            orders={sellersState.orders}
            collections={collections}
            loading={sellersState.loading}
            loadingMore={sellersState.loadingMore}
            emptyMessage="No orders found for sellers"
            onOrderClick={handleOrderClick}
            ref={sellersLoadMoreRef}
            activeTab={activeTab}
          />
        </TabsContent>

        <TabsContent value="buyers" className="space-y-4">
          <MarketplaceOrderList
            variant="order"
            orders={buyersState.orders}
            collections={collections}
            loading={buyersState.loading}
            loadingMore={buyersState.loadingMore}
            emptyMessage="No orders found for buyers"
            onOrderClick={handleOrderClick}
            ref={buyersLoadMoreRef}
            activeTab={activeTab}
          />
        </TabsContent>
      </Tabs>

      <CreateOrderDrawer
        open={showCreateOrderDrawer}
        onOpenChange={setShowCreateOrderDrawer}
        userType={activeTab === 'sellers' ? UserType.SELLER : UserType.BUYER}
        onOrderCreated={handleOrderCreated}
      />

      <OrderDetailsDrawer
        open={showOrderDetailsDrawer}
        onOpenChange={setShowOrderDetailsDrawer}
        order={selectedOrder}
        userType={activeTab === 'sellers' ? UserType.SELLER : UserType.BUYER}
        onOrderAction={handleOrderAction}
      />

      <ResellOrderDrawer
        open={showResellDrawer}
        onOpenChange={setShowResellDrawer}
        collections={collections}
        onOrderResold={handleOrderResold}
      />
    </div>
  );
}
